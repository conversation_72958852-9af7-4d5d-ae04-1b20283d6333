"""
Currency formatting utilities for consistent price display across the application.
"""

from typing import Optional, Dict


class CurrencyFormatter:
    """Utility class for formatting prices with correct currency symbols and codes."""
    
    # Common currency symbols mapping
    CURRENCY_SYMBOLS = {
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'JPY': '¥',
        'INR': '₹',
        'CAD': 'C$',
        'AUD': 'A$',
        'CHF': 'CHF',
        'CNY': '¥',
        'SEK': 'kr',
        'NOK': 'kr',
        'DKK': 'kr',
        'PLN': 'zł',
        'CZK': 'Kč',
        'HUF': 'Ft',
        'RUB': '₽',
        'BRL': 'R$',
        'MXN': '$',
        'ZAR': 'R',
        'KRW': '₩',
        'SGD': 'S$',
        'HKD': 'HK$',
        'NZD': 'NZ$',
        'THB': '฿',
        'MYR': 'RM',
        'PHP': '₱',
        'IDR': 'Rp',
        'VND': '₫',
        'TRY': '₺',
        'ILS': '₪',
        'AED': 'د.إ',
        'SAR': 'ر.س',
        'EGP': 'E£',
        'NGN': '₦',
        'KES': 'KSh',
        'GHS': '₵',
        'UGX': 'USh',
        'TZS': 'TSh',
        'ZMW': 'ZK',
        'BWP': 'P',
        'NAD': 'N$',
        'SZL': 'L',
        'LSL': 'L',
        'MWK': 'MK',
        'RWF': 'RF',
        'ETB': 'Br',
        'DJF': 'Fdj',
        'SOS': 'S',
        'ERN': 'Nfk',
        'SDG': 'ج.س',
        'SSP': '£',
        'LYD': 'ل.د',
        'TND': 'د.ت',
        'DZD': 'د.ج',
        'MAD': 'د.م',
        'MRU': 'UM',
        'SLL': 'Le',
        'LRD': 'L$',
        'CIV': 'CFA',
        'XOF': 'CFA',
        'XAF': 'FCFA',
        'GMD': 'D',
        'GNF': 'FG',
        'CVE': '$',
        'STN': 'Db',
        'AOA': 'Kz',
        'MZN': 'MT',
        'SZL': 'E',
        'MGA': 'Ar',
        'KMF': 'CF',
        'SCR': '₨',
        'MUR': '₨',
        'MVR': 'Rf',
        'LKR': '₨',
        'NPR': '₨',
        'BTN': 'Nu',
        'BDT': '৳',
        'PKR': '₨',
        'AFN': '؋',
        'IRR': '﷼',
        'IQD': 'ع.د',
        'JOD': 'د.ا',
        'KWD': 'د.ك',
        'LBP': 'ل.ل',
        'OMR': 'ر.ع',
        'QAR': 'ر.ق',
        'SYP': '£',
        'YER': '﷼',
        'BHD': '.د.ب',
        'AMD': '֏',
        'AZN': '₼',
        'GEL': '₾',
        'KZT': '₸',
        'KGS': 'лв',
        'TJS': 'SM',
        'TMT': 'T',
        'UZS': 'лв',
        'MNT': '₮',
        'LAK': '₭',
        'KHR': '៛',
        'MMK': 'K',
        'BND': 'B$',
        'FJD': 'FJ$',
        'PGK': 'K',
        'SBD': 'SI$',
        'TOP': 'T$',
        'VUV': 'VT',
        'WST': 'WS$',
        'XPF': '₣',
        'NCL': '₣',
        'PYG': '₲',
        'UYU': '$U',
        'CLP': '$',
        'COP': '$',
        'PEN': 'S/',
        'BOB': '$b',
        'VES': 'Bs',
        'GYD': 'G$',
        'SRD': '$',
        'TTD': 'TT$',
        'JMD': 'J$',
        'BBD': 'Bds$',
        'BZD': 'BZ$',
        'GTQ': 'Q',
        'HNL': 'L',
        'NIO': 'C$',
        'CRC': '₡',
        'PAB': 'B/.',
        'DOP': 'RD$',
        'HTG': 'G',
        'CUP': '₱',
        'XCD': 'EC$',
        'AWG': 'ƒ',
        'ANG': 'ƒ',
        'BMD': '$',
        'KYD': 'CI$',
        'BS': 'B$',
        'FK': '£',
        'GI': '£',
        'GG': '£',
        'IM': '£',
        'JE': '£',
        'SH': '£',
        'TC': '$',
        'VG': '$',
        'AI': 'EC$',
        'AG': 'EC$',
        'DM': 'EC$',
        'GD': 'EC$',
        'KN': 'EC$',
        'LC': 'EC$',
        'MS': 'EC$',
        'VC': 'EC$'
    }
    
    @classmethod
    def format_price(cls, amount: str, currency_code: Optional[str] = None, 
                    money_format: Optional[str] = None) -> str:
        """
        Format a price with the appropriate currency symbol or code.
        
        Args:
            amount: The price amount as a string (e.g., "10.00")
            currency_code: The ISO currency code (e.g., "USD", "INR")
            money_format: Shopify's money format string (e.g., "${{amount}}")
            
        Returns:
            Formatted price string (e.g., "₹10.00", "$10.00", "10.00 EUR")
        """
        if not amount:
            amount = "0.00"
            
        # Clean the amount string
        try:
            # Remove any existing currency symbols and clean the amount
            clean_amount = str(float(amount))
            # Format to 2 decimal places
            formatted_amount = f"{float(clean_amount):.2f}"
        except (ValueError, TypeError):
            formatted_amount = "0.00"
        
        # If we have a Shopify money format, use it
        if money_format and "{{amount}}" in money_format:
            return money_format.replace("{{amount}}", formatted_amount)
        
        # If we have a currency code, use our symbol mapping
        if currency_code:
            currency_code = currency_code.upper()
            symbol = cls.CURRENCY_SYMBOLS.get(currency_code)
            
            if symbol:
                # For most currencies, put symbol before amount
                if currency_code in ['EUR', 'GBP', 'JPY', 'INR', 'CNY', 'KRW', 'THB', 'VND']:
                    return f"{symbol}{formatted_amount}"
                else:
                    return f"{symbol}{formatted_amount}"
            else:
                # If no symbol found, use currency code after amount
                return f"{formatted_amount} {currency_code}"
        
        # Fallback: just return the amount with generic currency indicator
        return f"{formatted_amount}"
    
    @classmethod
    def get_currency_symbol(cls, currency_code: Optional[str]) -> str:
        """
        Get the currency symbol for a given currency code.
        
        Args:
            currency_code: The ISO currency code (e.g., "USD", "INR")
            
        Returns:
            Currency symbol or the currency code if symbol not found
        """
        if not currency_code:
            return "$"  # Default fallback
            
        currency_code = currency_code.upper()
        return cls.CURRENCY_SYMBOLS.get(currency_code, currency_code)
    
    @classmethod
    def format_price_with_currency_code(cls, amount: str, currency_code: Optional[str] = None) -> str:
        """
        Format a price with currency code (e.g., "10.00 USD").
        
        Args:
            amount: The price amount as a string
            currency_code: The ISO currency code
            
        Returns:
            Formatted price string with currency code
        """
        if not amount:
            amount = "0.00"
            
        try:
            formatted_amount = f"{float(amount):.2f}"
        except (ValueError, TypeError):
            formatted_amount = "0.00"
        
        if currency_code:
            return f"{formatted_amount} {currency_code.upper()}"
        else:
            return formatted_amount
