from sqlalchemy import Column, Integer, String, <PERSON><PERSON>an, DateTime, Text, ForeignKey, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import hashlib
import json

Base = declarative_base()

class User(Base):
    """User model for authentication and store ownership"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship to stores
    stores = relationship("Store", back_populates="user", cascade="all, delete-orphan")

class Store(Base):
    """Store model for Shopify store information"""
    __tablename__ = "stores"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Shopify store identifiers
    shopify_store_id = Column(String(255), nullable=False)  # Removed unique=True to allow multi-tenant sharing
    guid = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))

    # Store connection details
    shop_url = Column(String(255), nullable=False)  # e.g., "mystore.myshopify.com"
    access_token_encrypted = Column(Text, nullable=False)  # Base64 encoded access token

    # Store information
    store_name = Column(String(255))
    domain = Column(String(255))  # Primary domain
    myshopify_domain = Column(String(255))  # Shopify subdomain
    email = Column(String(255))

    # Currency information
    currency_code = Column(String(10))  # e.g., "USD", "INR", "EUR"
    money_format = Column(String(50))  # e.g., "${{amount}}", "₹{{amount}}"

    # Status and metadata
    status = Column(String(20), default="active")  # active, inactive, error
    is_deleted = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_sync = Column(DateTime(timezone=True))

    # Relationships
    user = relationship("User", back_populates="stores")
    assistants = relationship("Assistant", back_populates="store", cascade="all, delete-orphan")

class Assistant(Base):
    """Assistant model for tracking Pinecone assistants per store"""
    __tablename__ = "assistants"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)

    # Assistant identifiers
    assistant_name = Column(String(255), nullable=False)  # Removed unique=True to allow shared assistants
    pinecone_assistant_id = Column(String(255), unique=True, nullable=True)  # Allow NULL during migration

    # Assistant status and metadata
    status = Column(String(20), default="active")  # active, inactive, error, creating
    last_sync = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Data file tracking
    products_file_id = Column(String(255))
    orders_file_id = Column(String(255))
    customers_file_id = Column(String(255))
    products_hash = Column(String(64))
    orders_hash = Column(String(64))
    customers_hash = Column(String(64))

    # Relationships
    store = relationship("Store", back_populates="assistants")

def calculate_data_hash(data: dict) -> str:
    import hashlib
    import json
    def normalize_value(value):
        if isinstance(value, (int, float, str, bool)):
            return value
        elif isinstance(value, dict):
            return {k: normalize_value(v) for k, v in sorted(value.items())}
        elif isinstance(value, list):
            normalized_items = [normalize_value(item) for item in value]
            if normalized_items and isinstance(normalized_items[0], dict) and 'id' in normalized_items[0]:
                return sorted(normalized_items, key=lambda x: x.get('id', 0))
            return sorted(normalized_items)
        elif value is None:
            return None
        else:
            return str(value)
    normalized_data = normalize_value(data)
    data_str = json.dumps(normalized_data, sort_keys=True, ensure_ascii=False, separators=(',', ':'))
    return hashlib.md5(data_str.encode('utf-8')).hexdigest()
