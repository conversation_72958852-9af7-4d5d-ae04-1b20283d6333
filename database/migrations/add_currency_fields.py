"""
Migration to add currency fields to the stores table.

This migration adds:
- currency_code: Store's currency code (e.g., "USD", "INR", "EUR")
- money_format: Store's money format (e.g., "${{amount}}", "₹{{amount}}")

Run this script to update existing database schema.
"""

import sys
import os
from sqlalchemy import text

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from database.session import SessionLocal, engine


def add_currency_fields():
    """Add currency fields to the stores table."""
    
    # SQL commands to add the new columns
    add_currency_code_sql = """
    ALTER TABLE stores 
    ADD COLUMN currency_code VARCHAR(10);
    """
    
    add_money_format_sql = """
    ALTER TABLE stores 
    ADD COLUMN money_format VARCHAR(50);
    """
    
    db = SessionLocal()
    try:
        print("Adding currency_code column to stores table...")
        
        # Check if currency_code column already exists
        check_currency_code = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='stores' AND column_name='currency_code';
        """
        
        result = db.execute(text(check_currency_code)).fetchone()
        
        if not result:
            db.execute(text(add_currency_code_sql))
            print("✓ currency_code column added successfully")
        else:
            print("✓ currency_code column already exists")
        
        print("Adding money_format column to stores table...")
        
        # Check if money_format column already exists
        check_money_format = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='stores' AND column_name='money_format';
        """
        
        result = db.execute(text(check_money_format)).fetchone()
        
        if not result:
            db.execute(text(add_money_format_sql))
            print("✓ money_format column added successfully")
        else:
            print("✓ money_format column already exists")
        
        db.commit()
        print("\n✅ Migration completed successfully!")
        print("\nNote: Existing stores will have NULL currency values.")
        print("Currency information will be populated when stores are next synced or updated.")
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


def rollback_currency_fields():
    """Remove currency fields from the stores table (rollback)."""
    
    drop_currency_code_sql = "ALTER TABLE stores DROP COLUMN IF EXISTS currency_code;"
    drop_money_format_sql = "ALTER TABLE stores DROP COLUMN IF EXISTS money_format;"
    
    db = SessionLocal()
    try:
        print("Removing currency fields from stores table...")
        db.execute(text(drop_currency_code_sql))
        db.execute(text(drop_money_format_sql))
        db.commit()
        print("✅ Rollback completed successfully!")
        
    except Exception as e:
        print(f"❌ Rollback failed: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate currency fields in stores table")
    parser.add_argument("--rollback", action="store_true", 
                       help="Rollback the migration (remove currency fields)")
    
    args = parser.parse_args()
    
    if args.rollback:
        print("🔄 Rolling back currency fields migration...")
        rollback_currency_fields()
    else:
        print("🚀 Running currency fields migration...")
        add_currency_fields()
