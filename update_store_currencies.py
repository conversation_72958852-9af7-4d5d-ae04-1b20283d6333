"""
<PERSON><PERSON><PERSON> to update existing stores with currency information.

This script fetches currency data from Shopify for all existing stores
and updates the database with the correct currency_code and money_format.
"""

import sys
import os
from typing import List

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.session import SessionLocal
from database.crud import StoreCRUD
from database.models import Store
from stores.shopify_client import ShopifyAPIClient


def update_store_currency_info(store: Store) -> bool:
    """
    Update a single store's currency information.

    Args:
        store: Store object to update

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Decode access token
        access_token = StoreCRUD.decode_token(store.access_token_encrypted)

        # Create Shopify client
        client = ShopifyAPIClient(store.shop_url, access_token)

        # Get shop info with currency data
        shop_info = client.get_shop_info_graphql()

        if 'shop' not in shop_info:
            print(f"  ❌ Failed to get shop info for store {store.id}")
            return False

        shop_data = shop_info['shop']

        # Extract currency information
        currency_code = shop_data.get('currencyCode')
        money_format = shop_data.get('currencyFormats', {}).get('moneyFormat') if shop_data.get('currencyFormats') else None

        if not currency_code:
            print(f"  ⚠️  No currency code found for store {store.id}")
            return False

        # Update store with currency information
        db = SessionLocal()
        try:
            StoreCRUD.update_store(
                db,
                store.id,
                currency_code=currency_code,
                money_format=money_format
            )

            format_display = money_format if money_format else "None"
            print(f"  ✅ Updated store {store.id} with currency: {currency_code}, format: {format_display}")
            return True

        except Exception as e:
            print(f"  ❌ Database update failed for store {store.id}: {str(e)}")
            return False
        finally:
            db.close()

    except Exception as e:
        print(f"  ❌ Failed to update store {store.id}: {str(e)}")
        return False


def update_all_stores_currency():
    """Update currency information for all stores."""

    print("🔄 Updating currency information for all stores...\n")

    db = SessionLocal()
    try:
        # Get all active stores
        stores = db.query(Store).filter(
            Store.is_deleted == False,
            Store.status == 'active'
        ).all()

        if not stores:
            print("📭 No active stores found.")
            return

        print(f"📊 Found {len(stores)} active stores to update\n")

        success_count = 0
        failed_count = 0

        for i, store in enumerate(stores, 1):
            print(f"[{i}/{len(stores)}] Processing store {store.id} ({store.shop_url})...")

            # Skip if currency info already exists
            if store.currency_code:
                print(f"  ℹ️  Store already has currency: {store.currency_code}")
                success_count += 1
                continue

            # Update currency info
            if update_store_currency_info(store):
                success_count += 1
            else:
                failed_count += 1

            print()  # Empty line for readability

        print("=" * 50)
        print(f"📈 Update Summary:")
        print(f"  ✅ Successful: {success_count}")
        print(f"  ❌ Failed: {failed_count}")
        print(f"  📊 Total: {len(stores)}")

        if failed_count > 0:
            print(f"\n⚠️  {failed_count} stores failed to update.")
            print("   This could be due to:")
            print("   - Invalid access tokens")
            print("   - Network connectivity issues")
            print("   - Shopify API rate limits")
            print("   - Store access permissions")

        if success_count > 0:
            print(f"\n🎉 Successfully updated {success_count} stores!")
            print("   Currency information will now be used for price formatting.")

    except Exception as e:
        print(f"❌ Failed to update stores: {str(e)}")
        raise
    finally:
        db.close()


def list_stores_currency_status():
    """List all stores and their currency status."""

    print("📋 Store Currency Status\n")

    db = SessionLocal()
    try:
        stores = db.query(Store).filter(Store.is_deleted == False).all()

        if not stores:
            print("📭 No stores found.")
            return

        print(f"{'ID':<5} {'Shop URL':<30} {'Status':<10} {'Currency':<10} {'Format':<15} {'Updated'}")
        print("-" * 95)

        for store in stores:
            currency_display = store.currency_code if store.currency_code else "None"
            format_display = store.money_format if store.money_format else "None"
            updated_display = "Yes" if store.currency_code else "No"

            print(f"{store.id:<5} {store.shop_url:<30} {store.status:<10} {currency_display:<10} {format_display:<15} {updated_display}")

        # Summary
        total_stores = len(stores)
        updated_stores = len([s for s in stores if s.currency_code])
        pending_stores = total_stores - updated_stores

        print("-" * 95)
        print(f"Total stores: {total_stores}")
        print(f"With currency info: {updated_stores}")
        print(f"Pending update: {pending_stores}")

    except Exception as e:
        print(f"❌ Failed to list stores: {str(e)}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Update store currency information")
    parser.add_argument("--list", action="store_true",
                       help="List stores and their currency status")
    parser.add_argument("--update", action="store_true",
                       help="Update currency information for all stores")

    args = parser.parse_args()

    if args.list:
        list_stores_currency_status()
    elif args.update:
        update_all_stores_currency()
    else:
        print("Usage:")
        print("  python update_store_currencies.py --list    # List currency status")
        print("  python update_store_currencies.py --update  # Update all stores")
        print("\nNote: This script requires active store connections with valid access tokens.")
